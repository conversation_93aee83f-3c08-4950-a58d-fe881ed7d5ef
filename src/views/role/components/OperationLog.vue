<template>
    <div class="relative z-20">
        <el-table
            :data="dataSource"
            row-key="id"
            default-expand-all
            style="width: 100%"
            class="alarm-table"
        >
            <el-table-column
                prop="content"
                :label="$t('操作内容')"
                min-width="540"
            />
            <el-table-column
                prop="type"
                :label="$t('事件类型')"
                min-width="350"
            >
                <template #default="{ row }">
                    {{ getOperationName(row.operationLogModule) }} -
                    {{ row.operationLogTypeName }}
                </template>
            </el-table-column>
            <el-table-column
                prop="staffName"
                :label="$t('操作人')"
                min-width="140"
            />
            <el-table-column
                prop="createTime"
                :label="$t('发生时间')"
                min-width="300"
                align="right"
            />
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-wrapper">
            <el-pagination
                v-model:current-page="paginationProps.current"
                v-model:page-size="paginationProps.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="paginationProps.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { usePagenation } from '@/common/setup'
import apiVpp from '@/apiService/vpp'
import api from '@/apiService'

// 定义props
const props = defineProps({
    operationLogModule: {
        type: String,
        default: '',
    },
    dateSelect: {
        type: Object,
        default: () => ({
            periodType: 'day',
            startDate: '',
            endDate: '',
        }),
    },
})

// 定义emit
const emit = defineEmits(['update:dataSource'])

const dataSource = ref([])

const getData = async () => {
    const params = {
        current: pageParam.value.current,
        size: pageParam.value.size,
    }
    const res = await apiVpp.getOperationLogPage({
        ...params,
        ...props.dateSelect,
        operationLogModule: props.operationLogModule,
    })
    paginationProps.value.total = res.data.data.total
    paginationProps.value.current = res.data.data.current
    dataSource.value = res.data.data.records
    // 向父组件发送数据更新
    emit('update:dataSource', dataSource.value)
}

const { paginationProps, pageParam } = usePagenation(getData)

// 处理分页大小变化
const handleSizeChange = (val) => {
    pageParam.value.size = val
    pageParam.value.current = 1
    getData()
}

// 处理当前页变化
const handleCurrentChange = (val) => {
    pageParam.value.current = val
    getData()
}

// 监听props变化，重新获取数据
watch(
    () => [props.operationLogModule, props.dateSelect],
    () => {
        paginationProps.value.current = 1
        getData()
    },
    { deep: true }
)

const options = ref([])
const getTypeList = async () => {
    const res = await api.getDictByType({
        type: 'operationLogModule',
    })
    options.value = res.data.data
}
const getOperationName = (val) => {
    return options.value.find((item) => item.value == val)?.label
}
onMounted(async () => {
    await getTypeList()
    await getData()
})
</script>

<style lang="less" scoped>
// .ant-table-tbody > tr > td {
//     padding: 16px;
//     font-size: 14px;
//     color: var(--text-80);
// }

.alarm-table {
    // max-height: calc(100% - 80px);
    max-height: calc(~'100% - 48px');
    width: 100%;

    :deep(.el-table__row) {
        cursor: pointer;
    }

    :deep(th.el-table__cell) {
        background: none;
        font-weight: normal;
        color: var(--text-60) !important;
        padding: 12px 0;
    }

    :deep(td.el-table__cell) {
        padding: 12px 0;
        font-size: 14px;
        color: var(--text-80);
    }
    :deep(.cell) {
        line-height: 26px;
    }
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    padding: 16px 0;

    :deep(.el-pagination) {
        --el-pagination-font-size: 14px;
        --el-pagination-bg-color: transparent;
        --el-pagination-text-color: var(--text-80);
        --el-pagination-border-radius: 4px;
        --el-pagination-hover-color: var(--themeColor);
    }
}
</style>
