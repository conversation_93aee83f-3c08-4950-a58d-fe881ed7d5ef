<template>
    <div class="relative z-20">
        <el-table
            :data="dataSource"
            row-key="id"
            default-expand-all
            style="width: 100%"
            class="alarm-table"
        >
            <el-table-column
                prop="content"
                :label="$t('操作内容')"
                min-width="540"
            />
            <el-table-column
                prop="type"
                :label="$t('事件类型')"
                min-width="350"
            >
                <template #default="{ row }">
                    {{ getOperationName(row.operationLogModule) }} -
                    {{ row.operationLogTypeName }}
                </template>
            </el-table-column>
            <el-table-column
                prop="staffName"
                :label="$t('操作人')"
                min-width="140"
            />
            <el-table-column
                prop="createTime"
                :label="$t('发生时间')"
                min-width="300"
                align="right"
            />
        </el-table>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { usePagenation } from '@/common/setup'
import apiVpp from '@/apiService/vpp'
import api from '@/apiService'

// 定义props
const props = defineProps({
    operationLogModule: {
        type: String,
        default: '',
    },
    dateSelect: {
        type: Object,
        default: () => ({
            periodType: 'day',
            startDate: '',
            endDate: '',
        }),
    },
})

// 定义emit
const emit = defineEmits(['update:dataSource'])
const columns = [
    {
        title: '操作内容',
        dataIndex: 'content',
        key: 'content',
        slots: {
            customRender: 'content',
        },
        width: '40%',
    },
    {
        title: '事件类型',
        dataIndex: 'type',
        key: 'type',
        slots: {
            customRender: 'type',
        },

        align: 'left',
    },
    {
        title: '操作人',
        dataIndex: 'staffName',
        key: 'staffName',
        slots: {
            customRender: 'staffName',
        },

        align: 'center',
    },
    {
        title: '发生时间',
        dataIndex: 'createTime',
        key: 'createTime',
        slots: {
            customRender: 'createTime',
        },

        align: 'right',
    },
]
const dataSource = ref([])
const loading = ref(false)
const getData = async () => {
    const params = {
        current: pageParam.value.current,
        size: pageParam.value.size,
    }
    const res = await apiVpp.getOperationLogPage({
        ...params,
        ...props.dateSelect,
        operationLogModule: props.operationLogModule,
    })
    paginationProps.value.total = res.data.data.total
    paginationProps.value.current = res.data.data.current
    dataSource.value = res.data.data.records
    // 向父组件发送数据更新
    emit('update:dataSource', dataSource.value)
}
const { paginationProps, changePage, onTableChange, refresh, pageParam } =
    usePagenation(getData)

// 监听props变化，重新获取数据
watch(
    () => [props.operationLogModule, props.dateSelect],
    () => {
        paginationProps.value.current = 1
        getData()
    },
    { deep: true }
)

const options = ref([])
const getTypeList = async () => {
    const res = await api.getDictByType({
        type: 'operationLogModule',
    })
    options.value = res.data.data
}
const getOperationName = (val) => {
    return options.value.find((item) => item.value == val)?.label
}
onMounted(async () => {
    await getTypeList()
    await getData()
})
</script>

<style lang="less" scoped>
// .ant-table-tbody > tr > td {
//     padding: 16px;
//     font-size: 14px;
//     color: var(--text-80);
// }

.alarm-table {
    // max-height: calc(100% - 80px);
    max-height: calc(~'100% - 48px');
    width: 100%;

    :deep(.el-table__row) {
        cursor: pointer;
    }

    :deep(th.el-table__cell) {
        background: none;
        font-weight: normal;
        color: var(--text-60) !important;
        padding: 12px 0;
    }

    :deep(td.el-table__cell) {
        padding: 12px 0;
        font-size: 14px;
        color: var(--text-80);
    }
    :deep(.cell) {
        line-height: 26px;
    }
}
</style>
